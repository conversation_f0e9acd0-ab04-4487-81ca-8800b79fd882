package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/model"
)

type FileService struct {
	fileRepo   FileRepositoryInterface
	r2Service  R2ServiceInterface
	cdnService *CDNService
	config     *config.Config
}

func NewFileService(fileRepo FileRepositoryInterface, r2Service R2ServiceInterface, cdnService *CDNService, cfg *config.Config) *FileService {
	return &FileService{
		fileRepo:   fileRepo,
		r2Service:  r2Service,
		cdnService: cdnService,
		config:     cfg,
	}
}

// UploadFile handles file upload (both direct and signed URL)
func (s *FileService) UploadFile(ctx context.Context, input *model.UploadInput, userID *string) (*model.UploadResponse, error) {
	// Validate input
	if err := s.validateUploadInput(input); err != nil {
		return &model.UploadResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	// Generate unique filename
	fileID := uuid.New().String()
	ext := filepath.Ext(input.Filename)
	if ext == "" {
		// Try to get extension from mime type
		ext = s.getExtensionFromMimeType(input.MimeType)
	}
	uniqueFilename := fmt.Sprintf("%s%s", fileID, ext)

	// Create file metadata
	fileMetadata := &model.FileMetadata{
		ID:           fileID,
		Filename:     uniqueFilename,
		OriginalName: input.Filename,
		FileType:     input.FileType,
		MimeType:     input.MimeType,
		Size:         input.Size,
		Status:       model.FileStatusUploading,
		Tags:         input.Tags,
		Metadata:     input.Metadata,
		UserID:       userID,
	}

	// Generate URLs
	publicURL := s.r2Service.GetPublicURL(uniqueFilename)
	fileMetadata.PublicURL = &publicURL

	// Use CDN service to get optimized CDN URL if available
	if s.cdnService != nil {
		cdnURL := s.cdnService.GetCDNURL(publicURL)
		fileMetadata.CdnURL = &cdnURL
	} else if s.config.CDN.BaseURL != "" {
		cdnURL := fmt.Sprintf("%s/%s", s.config.CDN.BaseURL, uniqueFilename)
		fileMetadata.CdnURL = &cdnURL
	}

	// Save to database
	if err := s.fileRepo.Create(ctx, fileMetadata); err != nil {
		return &model.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to save file metadata: %v", err),
		}, nil
	}

	response := &model.UploadResponse{
		Success: true,
		Message: "File metadata created successfully",
		File:    fileMetadata,
	}

	// Generate signed upload URL if requested
	if input.UseSignedUpload {
		uploadURL, err := s.r2Service.GeneratePresignedUploadURL(
			ctx,
			uniqueFilename,
			input.MimeType,
			time.Duration(s.config.Upload.SignedURLExpiry)*time.Second,
		)
		if err != nil {
			return &model.UploadResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to generate upload URL: %v", err),
			}, nil
		}
		response.UploadURL = &uploadURL
		response.Message = "Signed upload URL generated successfully"
	}

	return response, nil
}

// DirectUpload handles direct file upload with file content
func (s *FileService) DirectUpload(ctx context.Context, input *model.UploadInput, fileContent io.Reader, userID *string) (*model.UploadResponse, error) {
	// First create the file metadata
	response, err := s.UploadFile(ctx, input, userID)
	if err != nil || !response.Success {
		return response, err
	}

	// Upload file to R2
	err = s.r2Service.UploadFile(ctx, response.File.Filename, fileContent, input.MimeType)
	if err != nil {
		// Update status to error
		response.File.Status = model.FileStatusError
		s.fileRepo.Update(ctx, response.File)

		return &model.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to upload file to storage: %v", err),
		}, nil
	}

	// Update status to ready
	response.File.Status = model.FileStatusReady
	if err := s.fileRepo.Update(ctx, response.File); err != nil {
		return &model.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update file status: %v", err),
		}, nil
	}

	response.Message = "File uploaded successfully"
	return response, nil
}

// CompleteUpload marks a signed upload as complete
func (s *FileService) CompleteUpload(ctx context.Context, fileID string) (*model.FileMetadata, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Check if file exists in R2
	exists, err := s.r2Service.FileExists(ctx, file.Filename)
	if err != nil {
		return nil, fmt.Errorf("failed to check file existence: %w", err)
	}

	if exists {
		file.Status = model.FileStatusReady
	} else {
		file.Status = model.FileStatusError
	}

	// Update status
	if err := s.fileRepo.Update(ctx, file); err != nil {
		return nil, fmt.Errorf("failed to update file status: %w", err)
	}

	return file, nil
}

// GetFile retrieves file metadata by ID
func (s *FileService) GetFile(ctx context.Context, fileID string) (*model.FileMetadata, error) {
	return s.fileRepo.GetByID(ctx, fileID)
}

// ListFiles retrieves files with filtering and pagination
func (s *FileService) ListFiles(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error) {
	// Set default pagination if not provided
	if pagination == nil {
		pagination = &model.PaginationInput{
			Limit:  20,
			Offset: 0,
		}
	}

	// Validate pagination
	if pagination.Limit <= 0 || pagination.Limit > 100 {
		pagination.Limit = 20
	}
	if pagination.Offset < 0 {
		pagination.Offset = 0
	}

	return s.fileRepo.List(ctx, filter, pagination)
}

// GenerateDownloadURL generates download URL (public or signed)
func (s *FileService) GenerateDownloadURL(ctx context.Context, fileID string, urlType model.URLType, expiresIn *int) (*model.DownloadResponse, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return &model.DownloadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get file: %v", err),
		}, nil
	}
	if file == nil {
		return &model.DownloadResponse{
			Success: false,
			Message: "File not found",
		}, nil
	}

	if file.Status != model.FileStatusReady {
		return &model.DownloadResponse{
			Success: false,
			Message: "File is not ready for download",
		}, nil
	}

	var url string
	var expiresAt *string

	switch urlType {
	case model.URLTypePublic:
		// Use CDN URL if available, otherwise public URL
		if file.CdnURL != nil {
			url = *file.CdnURL
		} else if file.PublicURL != nil {
			url = *file.PublicURL
		} else {
			url = s.r2Service.GetPublicURL(file.Filename)
		}
	case model.URLTypeSigned:
		expiry := time.Hour // Default 1 hour
		if expiresIn != nil && *expiresIn > 0 {
			expiry = time.Duration(*expiresIn) * time.Second
		}

		signedURL, err := s.r2Service.GeneratePresignedDownloadURL(ctx, file.Filename, expiry)
		if err != nil {
			return &model.DownloadResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to generate signed URL: %v", err),
			}, nil
		}
		url = signedURL

		expiresAtTime := time.Now().Add(expiry).Format(time.RFC3339)
		expiresAt = &expiresAtTime
	default:
		return &model.DownloadResponse{
			Success: false,
			Message: "Invalid URL type",
		}, nil
	}

	return &model.DownloadResponse{
		Success:   true,
		Message:   "Download URL generated successfully",
		URL:       &url,
		ExpiresAt: expiresAt,
	}, nil
}

// DeleteFile deletes a file and its metadata
func (s *FileService) DeleteFile(ctx context.Context, fileID string) (bool, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return false, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return false, fmt.Errorf("file not found")
	}

	// Delete from R2 (ignore errors if file doesn't exist)
	s.r2Service.DeleteFile(ctx, file.Filename)

	// Purge CDN cache if CDN service is available
	if s.cdnService != nil {
		urls := []string{}
		if file.PublicURL != nil {
			urls = append(urls, *file.PublicURL)
		}
		if file.CdnURL != nil {
			urls = append(urls, *file.CdnURL)
		}
		if len(urls) > 0 {
			// Ignore CDN purge errors, don't fail the delete operation
			s.cdnService.PurgeCache(ctx, urls)
		}
	}

	// Delete from database
	if err := s.fileRepo.Delete(ctx, fileID); err != nil {
		return false, fmt.Errorf("failed to delete file metadata: %w", err)
	}

	return true, nil
}

// UpdateFileMetadata updates file tags and metadata
func (s *FileService) UpdateFileMetadata(ctx context.Context, fileID string, tags []string, metadata *string) (*model.FileMetadata, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Update fields
	if tags != nil {
		file.Tags = tags
	}
	if metadata != nil {
		file.Metadata = metadata
	}

	// Save changes
	if err := s.fileRepo.Update(ctx, file); err != nil {
		return nil, fmt.Errorf("failed to update file metadata: %w", err)
	}

	return file, nil
}

// validateUploadInput validates upload input
func (s *FileService) validateUploadInput(input *model.UploadInput) error {
	if input.Filename == "" {
		return fmt.Errorf("filename is required")
	}

	if !input.FileType.IsValid() {
		return fmt.Errorf("invalid file type")
	}

	if input.MimeType == "" {
		return fmt.Errorf("mime type is required")
	}

	if input.Size <= 0 {
		return fmt.Errorf("file size must be greater than 0")
	}

	if input.Size > s.config.Upload.MaxFileSize {
		return fmt.Errorf("file size exceeds maximum allowed size")
	}

	// Validate file extension
	ext := strings.ToLower(filepath.Ext(input.Filename))
	if ext != "" {
		allowed := false
		for _, allowedExt := range s.config.Upload.AllowedExtensions {
			// Normalize allowed extension (remove dot if present)
			normalizedAllowedExt := strings.ToLower(allowedExt)
			if strings.HasPrefix(normalizedAllowedExt, ".") {
				normalizedAllowedExt = normalizedAllowedExt[1:]
			}

			// Compare with file extension (without dot)
			fileExt := ext[1:] // Remove the dot from file extension
			if fileExt == normalizedAllowedExt {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("file extension not allowed")
		}
	}

	return nil
}

// GetOptimizedImageURL returns an optimized image URL with transformation options
func (s *FileService) GetOptimizedImageURL(ctx context.Context, fileID string, options ImageOptimizationOptions) (*string, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Only optimize images
	if file.FileType != model.FileTypeImage {
		return nil, fmt.Errorf("file is not an image")
	}

	if file.Status != model.FileStatusReady {
		return nil, fmt.Errorf("file is not ready")
	}

	// Use CDN service for optimization if available
	if s.cdnService != nil {
		baseURL := ""
		if file.CdnURL != nil {
			baseURL = *file.CdnURL
		} else if file.PublicURL != nil {
			baseURL = *file.PublicURL
		} else {
			baseURL = s.r2Service.GetPublicURL(file.Filename)
		}

		optimizedURL := s.cdnService.GetOptimizedImageURL(baseURL, options)
		return &optimizedURL, nil
	}

	// Fallback to regular URL if CDN service not available
	if file.CdnURL != nil {
		return file.CdnURL, nil
	} else if file.PublicURL != nil {
		return file.PublicURL, nil
	}

	publicURL := s.r2Service.GetPublicURL(file.Filename)
	return &publicURL, nil
}

// getExtensionFromMimeType returns file extension based on mime type
func (s *FileService) getExtensionFromMimeType(mimeType string) string {
	switch mimeType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "image/gif":
		return ".gif"
	case "image/webp":
		return ".webp"
	case "image/svg+xml":
		return ".svg"
	case "video/mp4":
		return ".mp4"
	case "video/quicktime":
		return ".mov"
	case "video/x-msvideo":
		return ".avi"
	case "video/webm":
		return ".webm"
	default:
		return ""
	}
}
