# XBIT CDN Service Makefile

Server = .
ServerName = xbit-cdn-service

# Define variables
GOOS = linux   # Target OS (can be overridden)
GOARCH = amd64 # Target architecture
BinDir = ./bin

# Detect local platform for development
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

# Docker parameters
DOCKER_IMAGE=$(ServerName)
DOCKER_TAG=latest

build: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/server/main.go || { \
		echo "Build failed, trying to regenerate GraphQL code..."; \
		$(MAKE) gqlgen-fix; \
		cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/server/main.go; \
	}
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

build-local: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/server/main.go || { \
		echo "Build failed, trying to regenerate GraphQL code..."; \
		$(MAKE) gqlgen-fix; \
		cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/server/main.go; \
	}
	@echo "Server built for $(LOCAL_GOOS)/$(LOCAL_GOARCH) in $(BinDir)"

gqlgen-check:
	@if [ ! -f "graph/generated.go" ] || [ ! -f "graph/model/models_gen.go" ]; then \
		echo "Generated GraphQL files not found, generating..."; \
		$(MAKE) gqlgen; \
	fi

gqlgen:
	@echo "Generating GraphQL code..."
	@export PATH="$(shell go env GOPATH)/bin:$$PATH"; \
	if command -v gqlgen >/dev/null 2>&1; then \
		echo "Using gqlgen from PATH"; \
		gqlgen generate --config gqlgen.yml; \
		echo "GraphQL code generated"; \
	else \
		echo "Using go run instead of gqlgen..."; \
		go run github.com/99designs/gqlgen generate --config gqlgen.yml; \
		echo "GraphQL code generated with go run"; \
	fi

gqlgen-clean:
	@echo "Cleaning and regenerating GraphQL code..."
	@echo "Step 1: Backing up resolver files..."
	@if [ -f "graph/resolver.go" ]; then \
		mv graph/resolver.go graph/resolver.go.bak; \
	fi
	@echo "Step 2: Removing generated files..."
	@rm -f graph/generated.go
	@rm -f graph/model/models_gen.go
	@echo "Step 3: Generating fresh GraphQL code..."
	@go run github.com/99designs/gqlgen generate --config gqlgen.yml
	@echo "Step 4: Restoring resolver files..."
	@if [ -f "graph/resolver.go.bak" ]; then \
		mv graph/resolver.go.bak graph/resolver.go; \
	fi
	@echo "Step 5: Verifying build..."
	@go build ./graph/... || { \
		echo "Build verification failed. Please check for any remaining issues."; \
		exit 1; \
	}
	@echo "GraphQL code cleaned and regenerated successfully"

gqlgen-fix:
	@echo "Fixing GraphQL generation issues..."
	@$(MAKE) gqlgen

# Check GraphQL generation status
gqlgen-status:
	@echo "=== GraphQL Generation Status ==="
	@echo "Generated file exists: $(shell test -f graph/generated.go && echo 'YES' || echo 'NO')"
	@echo "Models file exists: $(shell test -f graph/model/models_gen.go && echo 'YES' || echo 'NO')"
	@echo "Schema files found:"
	@find . -name "*.graphqls" -o -name "*.gql" | head -10 || echo "No schema files found"
	@echo "Resolver files:"
	@ls -la graph/*.resolvers.go 2>/dev/null || echo "No resolver files found"
	@echo "Backup files:"
	@ls -la graph/*.resolvers.go.bak 2>/dev/null || echo "No backup files found"
	@echo "=== End Status ==="

# Prepare environment for GraphQL generation
gqlgen-prepare:
	@echo "Preparing environment for GraphQL generation..."
	@echo "Cleaning Go module cache..."
	@go clean -modcache
	@echo "Installing required dependencies..."
	@go get github.com/99designs/gqlgen@latest
	@go get golang.org/x/tools/go/packages@latest
	@go get github.com/urfave/cli/v2@latest
	@go mod download
	@go mod tidy
	@echo "Environment prepared successfully"

run:
	go run cmd/server/main.go

run-local:
	./scripts/run.sh local run

dev:
	./scripts/run.sh local dev

dev-air:
	air -c .air.toml

test:
	go test ./...

test-verbose:
	go test -v ./...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-unit:
	go test -v ./internal/service/... ./internal/handler/...

test-integration:
	go test -v -tags=integration ./...

test-watch:
	@echo "Running tests in watch mode (requires entr)..."
	find . -name "*.go" | entr -c go test ./...

clean:
	rm -rf $(BinDir)

install-deps:
	@echo "Installing dependencies..."
	go mod tidy
	go get github.com/99designs/gqlgen@latest
	go get golang.org/x/tools/go/packages@latest
	go mod tidy
	@echo "Dependencies installed"

# Database targets
db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash --dir file://migrations

db-fix-checksum:
	./scripts/fix-migration-checksum.sh

db-apply:
	./scripts/run.sh local migrate

db-apply-docker:
	./scripts/run.sh docker migrate

# Atlas migrations
db-apply-atlas:
	@if [ -z "$$DATABASE_URL" ]; then \
		echo "Error: DATABASE_URL environment variable is not set"; \
		echo "Please set DATABASE_URL or use 'make db-apply' instead"; \
		exit 1; \
	fi
	atlas migrate apply --url "$$DATABASE_URL" --dir file://migrations

db-apply-atlas-docker:
	atlas migrate apply --url "postgres://postgres:postgres@localhost:5432/cdn?sslmode=disable" --dir file://migrations

# Code formatting and linting
fix-imports:
	@echo "Fixing Go imports..."
	@go install golang.org/x/tools/cmd/goimports@latest
	@export PATH="$(shell go env GOPATH)/bin:$$PATH"; \
	if command -v goimports >/dev/null 2>&1; then \
		find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" | xargs goimports -w; \
		echo "Imports fixed with goimports"; \
	else \
		echo "Using go run instead of goimports..."; \
		find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" -exec go run golang.org/x/tools/cmd/goimports -w {} \;; \
		echo "Imports fixed with go run goimports"; \
	fi

format-code:
	@echo "Formatting Go code..."
	go fmt ./...
	@$(MAKE) fix-imports
	@echo "Code formatted"

# Debug and troubleshooting targets
debug-build:
	@echo "Debug build with verbose output..."
	mkdir -p $(BinDir)
	cd $(Server) && go build -v -x -o $(BinDir)/$(ServerName) cmd/server/main.go

debug-gqlgen:
	@echo "Debug GraphQL generation..."
	@echo "Current working directory: $(shell pwd)"
	@echo "GraphQL config file exists: $(shell test -f gqlgen.yml && echo 'YES' || echo 'NO')"
	@echo "Generated file exists: $(shell test -f graph/generated.go && echo 'YES' || echo 'NO')"
	@echo "Schema files:"
	@find . -name "*.graphqls" -o -name "*.gql" | head -10
	@echo "Running GraphQL generation with verbose output..."
	go run github.com/99designs/gqlgen generate --config gqlgen.yml --verbose

# Docker commands
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

# Local development commands (legacy - use new targets above)

# Environment-specific Docker Compose commands
.PHONY: local-up
local-up:
	docker-compose -f docker-compose.local.yml --env-file env/.env.local up -d

.PHONY: local-down
local-down:
	docker-compose -f docker-compose.local.yml down

.PHONY: local-logs
local-logs:
	docker-compose -f docker-compose.local.yml logs -f

.PHONY: unstable-up
unstable-up:
	docker-compose -f docker-compose.unstable.yml --env-file env/.env.unstable up -d

.PHONY: unstable-down
unstable-down:
	docker-compose -f docker-compose.unstable.yml down

.PHONY: unstable-logs
unstable-logs:
	docker-compose -f docker-compose.unstable.yml logs -f

# Verify R2 credentials for unstable environment
.PHONY: verify-r2-unstable
verify-r2-unstable:
	@echo "Verifying R2 credentials for unstable environment..."
	$(GOCMD) run scripts/verify-r2-credentials.go

# Setup R2 bucket for unstable environment
.PHONY: setup-r2-unstable
setup-r2-unstable:
	@echo "Setting up R2 bucket for unstable environment..."
	$(GOCMD) run scripts/setup-r2-bucket.go

# Test R2 connection for unstable environment
.PHONY: test-r2-unstable
test-r2-unstable:
	@echo "Testing R2 connection for unstable environment..."
	$(GOCMD) run scripts/test-r2-connection.go

.PHONY: staging-up
staging-up:
	docker-compose -f docker-compose.staging.yml --env-file env/.env.staging up -d

.PHONY: staging-down
staging-down:
	docker-compose -f docker-compose.staging.yml down

.PHONY: staging-logs
staging-logs:
	docker-compose -f docker-compose.staging.yml logs -f

.PHONY: production-up
production-up:
	docker-compose -f docker-compose.production.yml --env-file env/.env.production up -d

.PHONY: production-down
production-down:
	docker-compose -f docker-compose.production.yml down

.PHONY: production-logs
production-logs:
	docker-compose -f docker-compose.production.yml logs -f

# Legacy docker-compose commands (for backward compatibility)
.PHONY: docker-compose-up
docker-compose-up: local-up

.PHONY: docker-compose-down
docker-compose-down: local-down

.PHONY: docker-compose-logs
docker-compose-logs: local-logs

# Atlas Migration Commands (Local Development Only) - Legacy

# Atlas setup command
.PHONY: atlas-setup
atlas-setup:
	@echo "Setting up Atlas development database for local environment..."
	@./scripts/setup-atlas-dev-db.sh

# Legacy Database commands (for backward compatibility)
.PHONY: db-migrate-local
db-migrate-local: db-apply

.PHONY: db-migrate-unstable
db-migrate-unstable:
	@echo "Unstable migrations are handled by DevOps CI/CD pipeline"
	@echo "Please contact DevOps team for unstable database migrations"

.PHONY: db-migrate-staging
db-migrate-staging:
	@echo "Staging migrations are handled by DevOps CI/CD pipeline"
	@echo "Please contact DevOps team for staging database migrations"

.PHONY: db-migrate-production
db-migrate-production:
	@echo "Production migrations are handled by DevOps CI/CD pipeline"
	@echo "Please contact DevOps team for production database migrations"

# Legacy database commands (for backward compatibility)
.PHONY: db-migrate
db-migrate: db-migrate-local

.PHONY: db-reset
db-reset:
	@echo "Resetting database..."
	@if [ -f env/.env.local ]; then \
		export $$(cat env/.env.local | xargs) && \
		psql "postgresql://$$POSTGRES_AGENCY_USER:$$POSTGRES_AGENCY_PASS@$$POSTGRES_AGENCY_HOST:$$POSTGRES_AGENCY_PORT/$$POSTGRES_DB?sslmode=$$POSTGRES_AGENCY_SSL_MODE" -c "DROP TABLE IF EXISTS files CASCADE;"; \
		make db-migrate-local; \
	else \
		echo "Please create env/.env.local file with database configuration"; \
	fi

# Linting and formatting
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

.PHONY: lint
lint:
	golangci-lint run

# Generate GraphQL code (if using gqlgen)
.PHONY: generate
generate:
	$(GOCMD) run github.com/99designs/gqlgen generate

# Install development tools
.PHONY: install-tools
install-tools:
	$(GOGET) github.com/cosmtrek/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/99designs/gqlgen@latest

# Environment-specific setup commands
.PHONY: setup-local
setup-local: install-tools deps
	@echo "Setting up local development environment..."
	@if [ ! -f env/.env.local ]; then \
		mkdir -p env && \
		cp .env.local env/.env.local; \
		echo "Created env/.env.local - please configure your settings"; \
	fi
	@echo "Local development environment setup complete!"
	@echo "1. Configure env/.env.local with your settings"
	@echo "2. Run 'make local-up' to start dependencies"
	@echo "3. Run 'make db-migrate-local' to setup database"
	@echo "4. Run 'make dev' to start development server"

.PHONY: setup-unstable
setup-unstable:
	@echo "Setting up unstable environment..."
	@if [ ! -f env/.env.unstable ]; then \
		mkdir -p env && \
		cp .env.unstable env/.env.unstable; \
		echo "Created env/.env.unstable - please configure your settings"; \
	fi
	@echo "Unstable environment setup complete!"

.PHONY: setup-staging
setup-staging:
	@echo "Setting up staging environment..."
	@if [ ! -f env/.env.staging ]; then \
		mkdir -p env && \
		cp .env.staging env/.env.staging; \
		echo "Created env/.env.staging - please configure your settings"; \
	fi
	@echo "Staging environment setup complete!"

.PHONY: setup-production
setup-production:
	@echo "Setting up production environment..."
	@if [ ! -f env/.env.production ]; then \
		mkdir -p env && \
		cp .env.production env/.env.production; \
		echo "Created env/.env.production - please configure your settings"; \
	fi
	@echo "Production environment setup complete!"

# Legacy setup command (for backward compatibility)
.PHONY: setup
setup: setup-local

# Environment-specific deployment commands
.PHONY: deploy-unstable
deploy-unstable: docker-build
	@echo "Deploying to unstable environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

.PHONY: deploy-staging
deploy-staging: docker-build
	@echo "Deploying to staging environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

.PHONY: deploy-production
deploy-production: docker-build
	@echo "Deploying to production environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

# Legacy deploy command (for backward compatibility)
.PHONY: deploy
deploy: deploy-production

# Environment-specific health checks
.PHONY: health-local
health-local:
	@curl -f http://localhost:8080/health || echo "Local service is not healthy"

.PHONY: health-unstable
health-unstable:
	@curl -f https://unstable-api.xbit.com/health || echo "Unstable service is not healthy"

.PHONY: health-staging
health-staging:
	@curl -f https://staging-api.xbit.com/health || echo "Staging service is not healthy"

.PHONY: health-production
health-production:
	@curl -f https://api.xbit.com/health || echo "Production service is not healthy"

# Legacy health check (for backward compatibility)
.PHONY: health
health: health-local

# Icon upload targets
.PHONY: upload-icons
upload-icons:
	@echo "Uploading all icons from images/icons directory..."
	@./scripts/upload-icons.sh

.PHONY: upload-icons-preview
upload-icons-preview: upload-icons
	@echo "Opening icon preview in browser..."
	@if command -v open >/dev/null 2>&1; then \
		open icon-upload-preview.html; \
	elif command -v xdg-open >/dev/null 2>&1; then \
		xdg-open icon-upload-preview.html; \
	else \
		echo "Please open icon-upload-preview.html in your browser"; \
	fi

.PHONY: list-icons
list-icons:
	@echo "Available icons in images/icons:"
	@find images/icons -name "*.svg" -type f | sort | while read file; do \
		filename=$$(basename "$$file"); \
		size=$$(stat -f%z "$$file" 2>/dev/null || stat -c%s "$$file" 2>/dev/null); \
		echo "  📄 $$filename ($${size} bytes)"; \
	done

.PHONY: clean-icons
clean-icons:
	@echo "Cleaning icon upload results..."
	@rm -f upload-results.json icon-upload-preview.html
	@echo "Icon upload results cleaned"

.PHONY: test-upload-icon
test-upload-icon:
	@echo "Testing upload of a single icon..."
	@./scripts/test-upload-single.sh $(ICON_NAME)

# Help target
.PHONY: help
help:
	@echo "=== xbit-cdn-service Makefile Help ==="
	@echo ""
	@echo "Build targets:"
	@echo "  build              - Build the server for Linux"
	@echo "  build-local        - Build the server for local platform"
	@echo "  run                - Run the server locally"
	@echo "  dev                - Run the server in development mode"
	@echo "  clean              - Clean build artifacts"
	@echo ""
	@echo "GraphQL targets:"
	@echo "  gqlgen             - Generate GraphQL code (robust, handles resolver backup/restore)"
	@echo "  gqlgen-clean       - Clean and regenerate GraphQL code"
	@echo "  gqlgen-fix         - Fix GraphQL generation issues (alias for gqlgen)"
	@echo "  gqlgen-status      - Check GraphQL generation status"
	@echo "  gqlgen-prepare     - Prepare environment for GraphQL generation"
	@echo "  gqlgen-check       - Check if GraphQL generation is needed"
	@echo ""
	@echo "Testing targets:"
	@echo "  test               - Run all tests"
	@echo "  test-verbose       - Run tests with verbose output"
	@echo "  test-coverage      - Run tests with coverage report"
	@echo "  test-unit          - Run unit tests only"
	@echo "  test-integration   - Run integration tests only"
	@echo "  test-watch         - Run tests in watch mode"
	@echo ""
	@echo "Database targets:"
	@echo "  db-diff            - Generate database migration diff"
	@echo "  db-rehash          - Rehash database migrations"
	@echo "  db-apply           - Apply database migrations"
	@echo "  db-apply-docker    - Apply database migrations in Docker"
	@echo "  db-apply-atlas     - Apply migrations using Atlas"
	@echo "  db-apply-atlas-docker - Apply migrations using Atlas in Docker"
	@echo ""
	@echo "Development targets:"
	@echo "  install-deps       - Install dependencies"
	@echo "  fix-imports        - Fix Go imports using goimports"
	@echo "  format-code        - Format Go code and fix imports"
	@echo "  debug-build        - Debug build with verbose output"
	@echo "  debug-gqlgen       - Debug GraphQL generation"
	@echo ""
	@echo "Docker targets:"
	@echo "  docker-build       - Build Docker image"
	@echo "  docker-run         - Run Docker container"
	@echo "  local-up           - Start local environment"
	@echo "  local-down         - Stop local environment"
	@echo "  local-logs         - Show local environment logs"
	@echo ""
	@echo "Environment targets:"
	@echo "  setup-local        - Setup local development environment"
	@echo "  setup-unstable     - Setup unstable environment"
	@echo "  setup-staging      - Setup staging environment"
	@echo "  setup-production   - Setup production environment"
	@echo ""
	@echo "Health checks:"
	@echo "  health-local       - Check local service health"
	@echo "  health-unstable    - Check unstable service health"
	@echo "  health-staging     - Check staging service health"
	@echo "  health-production  - Check production service health"
	@echo ""
	@echo "Icon upload targets:"
	@echo "  upload-icons       - Upload all SVG icons from images/icons"
	@echo "  upload-icons-preview - Upload icons and open preview in browser"
	@echo "  list-icons         - List all available icons in images/icons"
	@echo "  test-upload-icon   - Test upload a single icon (use ICON_NAME=filename.svg)"
	@echo "  clean-icons        - Clean icon upload results"
	@echo ""
	@echo "Usage: make <target>"
	@echo "Example: make gqlgen"

.PHONY: build build-local gqlgen-check gqlgen gqlgen-clean gqlgen-fix gqlgen-status gqlgen-prepare install-deps db-diff db-rehash db-fix-checksum db-apply db-apply-docker db-apply-atlas db-apply-atlas-docker fix-imports format-code debug-build debug-gqlgen run dev test test-verbose test-coverage test-unit test-integration test-watch clean help
