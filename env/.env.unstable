# Unstable Environment Variables
ENV=unstable

# Server Configuration
PORT=8080
HOST=0.0.0.0
DEBUG=true
LOG_LEVEL=info

# Database Configuration
POSTGRES_AGENCY_HOST=127.0.0.1
POSTGRES_AGENCY_PORT=5433
POSTGRES_AGENCY_USER=postgres
POSTGRES_AGENCY_PASS=postgres
POSTGRES_DB=xbit_cdn
POSTGRES_AGENCY_SSL_MODE=disable
DB_MAX_CONNECTIONS=20
DB_MAX_IDLE_CONNECTIONS=10

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=23730d7e6dec9dfe0266e9df554e20c5
R2_ACCESS_KEY_ID=659727ae7f867e3a8430d26d8200b6dc
R2_SECRET_ACCESS_KEY=3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50
R2_BUCKET_NAME=xbit-unstable
R2_ENDPOINT=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Cloudflare CDN Configuration
CDN_BASE_URL=https://unstable-cdn.xbit.com
CDN_ZONE_ID=CHANGE_ME_CDN_ZONE_ID
CDN_API_TOKEN=CHANGE_ME_CDN_API_TOKEN

# JWT Configuration
JWT_SECRET=CHANGE_ME_UNSTABLE_JWT_SECRET_KEY
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# File Upload Configuration
MAX_FILE_SIZE=500MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm,pdf,doc,docx,zip,rar
SIGNED_URL_EXPIRY=2h
TEMP_DIR=/tmp/xbit-uploads

# Redis Configuration
REDIS_HOST=unstable-redis.internal
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD
REDIS_DB=0
REDIS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://unstable.xbit.com,https://admin-unstable.xbit.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROFILING_ENABLED=false

# Security Configuration
ENABLE_HTTPS=true
HSTS_ENABLED=true
CSRF_PROTECTION=true

# Rate Limiting
RATE_LIMITING_ENABLED=true
REQUESTS_PER_MINUTE=1000
BURST_SIZE=100

# Frontend URLs
FRONTEND_URL=https://unstable.xbit.com
ADMIN_URL=https://admin-unstable.xbit.com

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
