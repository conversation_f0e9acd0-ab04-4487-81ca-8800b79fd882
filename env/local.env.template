# XBIT CDN Service - Local Environment Configuration Template
# Copy this file to .env.local and update the values

# Application Configuration
APP_ENV=local
SERVER_PORT=8080

# Database Configuration
POSTGRES_DB=xbit_cdn
POSTGRES_AGENCY_HOST=127.0.0.1
POSTGRES_AGENCY_PORT=5433
POSTGRES_AGENCY_USER=postgres
POSTGRES_AGENCY_PASS=postgres
POSTGRES_AGENCY_SSL_MODE=disable

# JWT Configuration
JWT_SECRET=xbit-cdn-service-local-secret
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_BUCKET_NAME=
R2_REGION=auto
R2_ENDPOINT=

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=console
