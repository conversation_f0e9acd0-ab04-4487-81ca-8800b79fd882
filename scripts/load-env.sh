#!/bin/bash

# Load environment variables for XBIT CDN Service
# Usage: source ./scripts/load-env.sh [environment]

ENV=${1:-local}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Environment file paths
ENV_FILE="$PROJECT_DIR/env/.env.$ENV"
DEFAULT_ENV_FILE="$PROJECT_DIR/.env"

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment from $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
elif [ -f "$DEFAULT_ENV_FILE" ]; then
    echo "Loading environment from $DEFAULT_ENV_FILE"
    set -a
    source "$DEFAULT_ENV_FILE"
    set +a
else
    echo "No environment file found for $ENV"
    echo "Expected: $ENV_FILE or $DEFAULT_ENV_FILE"
fi

# Set default values if not set
export APP_ENV=${APP_ENV:-$ENV}
export SERVER_PORT=${SERVER_PORT:-8080}
export POSTGRES_DB=${POSTGRES_DB:-xbit_cdn}
export POSTGRES_AGENCY_HOST=${POSTGRES_AGENCY_HOST:-127.0.0.1}
export POSTGRES_AGENCY_PORT=${POSTGRES_AGENCY_PORT:-5433}
export POSTGRES_AGENCY_USER=${POSTGRES_AGENCY_USER:-postgres}
export POSTGRES_AGENCY_PASS=${POSTGRES_AGENCY_PASS:-postgres}
export POSTGRES_AGENCY_SSL_MODE=${POSTGRES_AGENCY_SSL_MODE:-disable}

# Construct DATABASE_URL if not set
if [ -z "$DATABASE_URL" ]; then
    export DATABASE_URL="postgres://${POSTGRES_AGENCY_USER}:${POSTGRES_AGENCY_PASS}@${POSTGRES_AGENCY_HOST}:${POSTGRES_AGENCY_PORT}/${POSTGRES_DB}?sslmode=${POSTGRES_AGENCY_SSL_MODE}"
fi

echo "Environment: $APP_ENV"
echo "Port: $SERVER_PORT"
echo "Database: $POSTGRES_DB"
